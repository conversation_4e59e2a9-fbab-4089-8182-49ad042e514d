<!doctype html>
<html lang="en" class="scroll-smooth">
  <head>
    <!-- Meta Tags start -->
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Meta Tags end -->

    <!-- Website name start -->
    <title>My Portfolio</title>
    <!-- Website name end -->

    <!-- Use Tailwind CSS start -->
    <link href="dist/css/output.css" rel="stylesheet" />
    <!-- Use Tailwind CSS end -->

    <!-- Dark mode start -->
    <script>
      if (
        localStorage.theme === "dark" ||
        (!("theme" in localStorage) &&
          window.matchMedia("(prefers-color-scheme: dark)").matches)
      ) {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }
    </script>
    <!-- Dark mode end -->
  </head>
  <body>
    <!-- Header Start -->
    <header
      class="absolute top-0 left-0 z-10 flex w-full items-center bg-transparent"
    >
      <div class="container">
        <div class="relative flex items-center justify-between">
          <div class="px-4">
            <a href="#home" class="block py-6 text-lg font-bold text-primary"
              >Muhammad Syaamil Muzhaffar</a
            >
          </div>
          <div class="flex items-center px-4">
            <!-- Mobile Hamburger menu start -->
            <button
              id="hamburger"
              name="hamburger"
              type="button"
              class="absolute right-4 block lg:hidden"
            >
              <span
                class="hamburger-line origin-top-left transition duration-300 ease-in-out"
              ></span>
              <span
                class="hamburger-line transition duration-300 ease-in-out"
              ></span>
              <span
                class="hamburger-line origin-bottom-left transition duration-300 ease-in-out"
              ></span>
            </button>
            <!-- Mobile Hamburger menu end -->

            <!-- Navigation Menu start -->
            <nav
              id="nav-menu"
              class="absolute right-4 top-full hidden w-full max-w-[250px] rounded-lg bg-white py-5 shadow-lg dark:bg-dark dark:shadow-slate-500 lg:static lg:block lg:max-w-full lg:rounded-none lg:bg-transparent lg:shadow-none lg:dark:bg-transparent"
            >
              <ul class="block lg:flex">
                <!-- Beranda start -->
                <li class="group">
                  <a
                    href="#home"
                    class="mx-8 flex py-2 text-base text-dark group-hover:text-primary dark:text-white"
                    >Beranda</a
                  >
                </li>
                <!-- Beranda end -->

                <!-- Tentang Saya start-->
                <li class="group">
                  <a
                    href="#about"
                    class="mx-8 flex py-2 text-base text-dark group-hover:text-primary dark:text-white"
                    >Tentang Saya</a
                  >
                </li>
                <!-- Tentang Saya end-->

                <!-- Skill start -->
                <li class="group">
                  <a
                    href="#skill"
                    class="mx-8 flex py-2 text-base text-dark group-hover:text-primary dark:text-white"
                    >Skill</a
                  >
                </li>
                <!-- Skill end -->

                <!-- Portfolio start -->
                <li class="group">
                  <a
                    href="#portfolio"
                    class="mx-8 flex py-2 text-base text-dark group-hover:text-primary dark:text-white"
                    >Portfolio</a
                  >
                </li>
                <!-- Portfolio end -->

                <!-- Tech Stack start -->
                <li class="group">
                  <a
                    href="#techstack"
                    class="mx-8 flex py-2 text-base text-dark group-hover:text-primary dark:text-white"
                    >Tech Stack</a
                  >
                </li>
                <!-- Tech Stack end -->

                <!-- Contact start -->
                <li class="group">
                  <a
                    href="#contact"
                    class="mx-8 flex py-2 text-base text-dark group-hover:text-primary dark:text-white"
                    >Contact</a
                  >
                </li>
                <!-- Contact end -->

                <!-- Dark mode on and off start -->
                <li class="mt-3 flex items-center pl-8 lg:mt-0">
                  <div class="flex">
                    <span class="mr-2 text-sm text-slate-500">Light</span>
                    <input type="checkbox" class="hidden" id="dark-toggle" />
                    <label for="dark-toggle">
                      <div
                        class="flex h-5 w-9 cursor-pointer items-center rounded-full bg-slate-500 p-1"
                      >
                        <div
                          class="toggle-circle h-4 w-4 rounded-full bg-white transition duration-300 ease-in-out"
                        ></div>
                      </div>
                    </label>
                    <span class="ml-2 text-sm text-slate-500">Dark</span>
                  </div>
                </li>
                <!-- Dark mode on and off end -->
              </ul>
            </nav>
          </div>
        </div>
      </div>
    </header>
    <!-- Header End -->

    <!-- Hero Section Start -->
    <section id="home" class="pt-36 dark:bg-dark">
      <div class="container">
        <div class="flex flex-wrap">
          <div class="w-full self-center px-4 lg:w-1/2">
            <h1 class="text-base font-semibold text-primary md:text-xl">
              Halo Semua 👋, Saya
              <span
                class="mt-1 block text-2xl font-bold text-dark dark:text-white lg:text-3xl"
                >Muhammad Syaamil Muzhaffar</span
              >
            </h1>
            <h2 class="mb-5 text-lg font-medium text-secondary lg:text-1xl">
              AI Prompt Engineering | Cyber Security | UI Designer
            </h2>
            <p class="mb-10 font-medium leading-relaxed text-secondary">
              Hello World!
            </p>

            <!-- Hubungi Saya start -->
            <a
              href="#contact"
              class="rounded-full bg-primary py-3 px-8 text-base font-semibold text-white transition duration-300 ease-in-out hover:opacity-80 hover:shadow-lg"
              >Hubungi Saya</a
            >
            <!-- Hubungi Saya start -->
          </div>
          <div class="w-full self-end px-4 lg:w-1/2">
            <div class="relative mt-10 lg:right-0 lg:mt-9">
              <!-- My Image start -->
              <img
                src="assets/images/profile/muhammadsyaamilmuzhaffar.png"
                alt="Muhammad Syaamil Muzhaffar"
                class="relative z-10 mx-auto max-w-full"
              />
              <!-- My Image end -->

              <!-- Blobmaker start -->
              <span
                class="absolute bottom-0 left-1/2 -translate-x-1/2 md:scale-125"
              >
                <svg
                  width="300"
                  height="300"
                  viewBox="0 0 200 200"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill="#facc15"
                    d="M47.4,-51C59.2,-35.6,65.1,-17.8,63.4,-1.7C61.7,14.5,52.6,28.9,40.8,36.6C28.9,44.2,14.5,45,-2,47C-18.4,49,-36.8,52.1,-45.4,44.5C-53.9,36.8,-52.5,18.4,-51.2,1.3C-49.9,-15.8,-48.7,-31.7,-40.2,-47.1C-31.7,-62.4,-15.8,-77.3,1,-78.3C17.8,-79.2,35.6,-66.3,47.4,-51Z"
                    transform="translate(100 100) scale(1.1)"
                  />
                </svg>
              </span>
              <!-- Blobmaker end -->
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Hero Section End -->

    <!-- About Section Start -->
    <section id="about" class="pt-36 pb-32 dark:bg-dark">
      <div class="container">
        <div class="flex flex-wrap">
          <div class="mb-10 w-full px-4 lg:w-1/2">
            <h4 class="mb-3 text-lg font-bold uppercase text-primary">
              Tentang Saya
            </h4>
            <h2
              class="mb-5 max-w-md text-2xl font-bold text-dark dark:text-white lg:text-3xl"
            >
              Siapa Saya?
            </h2>
            <p class="max-w-xl text-base font-medium text-secondary lg:text-lg">
              Hai! Saya Muhammad Syaamil Muzhaffar, seorang mahasiswa Sistem
              Informasi di Universitas Mercu Buana dengan berfokus pada
              pengembangan UI/UX Designer di bagian Web, Android, dan iOS.
              Dengan pengalaman dalam merancang dan mengembangkan produk
              digital, saya juga suka membuat proyek pribadi untuk melatih
              kemampuan saya. Selain itu, saya suka mengikuti kompetisi untuk
              menantang diri saya dan mengasah kemampuan yang sudah saya
              pelajari. Saya menguasai tools desain seperti Figma dan saya juga
              menguasai berbagai teknologi seperti HTML, CSS, JavaScript, Git,
              dan framework CSS seperti Tailwind CSS. Untuk mengenal lebih dalam
              tentang kemampuan saya, Anda bisa mengunjungi link berikut ini
              <a href="https://lynk.id/musyaml" class="hover:text-primary"
                >https://lynk.id/musyaml</a
              >. Saya memiliki mindset yang terus belajar dan beradaptasi dengan
              perkembangan teknologi terbaru. Selain itu, saya juga memiliki
              keahlian seperti komunikasi yang baik, empati, dan kemampuan untuk
              bekerja dalam tim. Untuk itu saya berharap dapat bekerja sama
              dengan Anda!
            </p>
          </div>
          <div class="w-full px-4 lg:w-1/2">
            <h3
              class="mb-4 text-2xl font-semibold text-dark dark:text-white lg:pt-10 lg:text-3xl"
            >
              Ingin mengenal Saya lebih jauh?
            </h3>
            <p class="mb-6 text-base font-medium text-secondary lg:text-lg">
              Kunjungi sosial media Saya.
            </p>
            <div class="flex items-center">
              <!-- Credly start -->
              <a
                href="https://www.credly.com/users/musyaml"
                target="_blank"
                class="mr-3 flex h-9 w-9 items-center justify-center rounded-full border border-slate-300 text-slate-300 hover:border-primary hover:bg-primary hover:text-white"
              >
                <svg
                  role="img"
                  width="20"
                  class="fill-current"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Credly</title>
                  <path
                    d="M23.8 13.154a.299.299 0 0 0-.101-.024.407.407 0 0 0-.202.048c-.06.028-.092.08-.127.136-.087.128-.15.268-.226.4-.107.187-.246.351-.38.515-.135.156-.286.291-.424.44-.028.027-.072.043-.107.027-.028-.016-.036-.056-.032-.088.04-.38.075-.763.123-1.138.02-.172.043-.336.063-.512.028-.247.056-.487.087-.735l.234-1.824c.02-.128.032-.372-.135-.52a.446.446 0 0 0-.233-.116.46.46 0 0 0-.254.06c-.226.16-.297.504-.365.76-.142.603-.178 1.241-.471 1.804a1.772 1.772 0 0 1-.202.316.668.668 0 0 1-.186.18.332.332 0 0 1-.246.051.365.365 0 0 1-.238-.207.871.87 0 0 1-.063-.324 4.499 4.499 0 0 1 .24-1.585c.045-.132.089-.252.104-.383.028-.156.028-.38-.114-.516-.131-.128-.337-.18-.504-.128-.194.056-.31.244-.372.392-.198.463-.25.95-.317 1.446-.044.327-.127.64-.293.926a2.717 2.717 0 0 1-.603.72c-.118.087-.222.123-.328.107a.376.376 0 0 1-.278-.208.875.875 0 0 1-.095-.315 3.361 3.36 0 0 1-.036-.616c.004-.223 0-.44.044-.658.075-.39.678-1.937.808-2.345.135-.407.262-.823.353-1.246.08-.38.123-.767.11-1.15-.007-.277-.07-.576-.288-.736a.611.61 0 0 0-.603-.048.968.968 0 0 0-.455.428 2.53 2.53 0 0 0-.226.59 12.01 12.01 0 0 0-.266 1.29c-.071.429-.138.848-.206 1.268-.06.355-.206 1.614-.261 1.88-.06.272-.175.54-.301.787-.131.268-.258.536-.408.791a.694.694 0 0 1-.175.224c-.08.06-.182.088-.27.048-.102-.048-.146-.176-.166-.292-.075-.435-.012-.875.072-1.302.083-.431.44-2.4.519-2.851.099-.532.24-1.05.285-1.59.028-.388.09-.88-.202-1.187-.115-.136-.31-.16-.44-.136-.174.036-.31.176-.388.296-.1.128-.186.28-.258.467-.115.284-.186.615-.261.91l-.032.129c-.083.383-.143.77-.186 1.162a16.95 16.948 0 0 0-.06.632c-.008.1-.016.203-.027.307 0 .08.007.168-.028.244a.304.304 0 0 1-.052.068c-.08.072-.202.06-.31.056-.557-.016-1.045.3-1.35.755-.18.252-.281.542-.39.834-.01.048-.034.1-.054.152-.051.143-.13.327-.222.511a3.037 3.037 0 0 1-.317.46 3.285 3.285 0 0 1-.384.41 1.123 1.123 0 0 1-.515.26c-.174.04-.384-.043-.543-.203a.916.916 0 0 1-.206-.54c-.004-.055-.004-.115.028-.163.05-.068.146-.072.23-.076a1.623 1.623 0 0 0 1.375-1.015c.138-.34.178-.698.122-1.046a1.193 1.193 0 0 0-.19-.48.9.9 0 0 0-.396-.323c-.293-.14-.658-.127-1.01.004-.575.232-.951.74-1.134 1.562l-.02.088c-.114.487-.23 1-.582 1.354-.127.12-.261.163-.368.143-.044-.004-.08-.04-.103-.075-.096-.16.003-.532.15-1a4.1 4.1 0 0 0 .1-.366.925.925 0 0 0-.108-.495.783.783 0 0 0-.372-.324c-.143-.064-.31-.06-.468-.06h-.047c-.044 0-.103 0-.151-.012a.215.215 0 0 1-.147-.127.485.485 0 0 1 .016-.232c.004-.02.012-.048.016-.072a.368.368 0 0 0-.162-.412.509.509 0 0 0-.468-.036.768.768 0 0 0-.364.348.769.769 0 0 0-.103.48c.04.13.07.32.043.475-.055.28-.222.51-.384.74-.04.05-.072.106-.107.16a4.96 4.96 0 0 1-.706.825c-.372.335-.804.575-1.232.67-.745.165-1.506-.06-1.91-.734-.222-.38-.32-.827-.348-1.266a5.425 5.425 0 0 1 .424-2.516c.328-.76.816-1.52 1.715-1.614.353-.04.753.083.912.4.115.23.075.506 0 .75-.072.244-.175.49-.18.75-.003.26.124.54.37.616.238.072.495-.08.634-.29.138-.21.186-.46.245-.704a6.282 6.281 0 0 1 .662-1.634c.139-.236.297-.488.254-.76a.543.543 0 0 0-.373-.415.543.543 0 0 0-.535.144c-.134.148-.206.371-.387.43-.17.06-.35-.055-.507-.134-.6-.32-1.336-.312-1.963-.048-.634.25-1.146.735-1.526 1.294C.462 8.53.098 9.508.022 10.48c-.027.34-.031.695 0 1.038.036.46.1.854.214 1.206.139.423.317.79.547 1.094.266.34.587.6.94.747.372.148.784.22 1.192.208a3.172 3.172 0 0 0 1.177-.283 4.29 4.29 0 0 0 1.026-.68c.309-.26.594-.559.84-.89.162-.224.309-.46.44-.708a4.83 4.83 0 0 0 .178-.383c.044-.104.087-.215.202-.26.056-.043.15-.02.202.013.064.04.115.075.135.135.048.116.02.232-.004.332v.012c-.028.1-.055.203-.091.303-.14.424-.238.811-.16 1.195.045.207.128.387.25.527a.84.84 0 0 0 .504.264c.246.04.51-.028.725-.132.143-.068.278-.156.397-.26.06-.06.122-.12.174-.184.044-.06.087-.147.178-.143a.15.15 0 0 1 .107.064c.028.031.04.071.06.115.23.52.776.84 1.335.84h.07c.27 0 .556-.093.79-.22.27-.14.48-.348.7-.552.02-.016.045-.04.073-.044.035-.008.07.012.099.044a.26.26 0 0 1 .047.1c.135.34.46.6.824.66a1.1 1.1 0 0 0 .99-.356c.056-.06.104-.128.167-.176.064-.044.15-.076.222-.044.107.04.135.164.182.268.107.235.357.371.615.375.289 0 .554-.148.764-.34.195-.183.353-.399.516-.61a.328.328 0 0 1 .106-.096c.04-.024.096-.028.13 0 .033.024.045.06.06.091.163.4.587.652 1.01.648.417-.004.809-.224 1.103-.516.095-.092.194-.2.32-.21.14-.017.207.114.254.22.072.142.115.238.25.338.158.116.36.152.547.1.17-.04.34-.156.47-.316.072-.088.112-.204.19-.284.092-.087.132.028.136.1.016.116.016.236.008.352-.016.236-.052.471-.08.703-.011.068-.02.136-.063.188-.06.068-.166.08-.253.064a2.898 2.898 0 0 0-.321-.028l-.14-.016c-.201-.012-.4-.036-.61-.044h-.185c-.404 0-.733.048-1.03.16-.48.187-.852.57-1.003 1.018a1.305 1.305 0 0 0-.052.64c.04.203.13.403.282.587.265.315.68.515 1.149.543.408.02.852-.064 1.292-.26.848-.367 1.482-1.094 1.696-1.95 0-.02.01-.039.023-.043.298-.104.57-.248.813-.428.245-.187.467-.399.65-.643.09-.12.174-.243.253-.37.07-.125.13-.257.202-.38a.906.906 0 0 0 .13-.316.411.411 0 0 0-.05-.328.257.257 0 0 0-.135-.124m-13.68-1.63c.017-.071.045-.14.06-.206a1.9 1.9 0 0 1 .262-.504c.04-.048.08-.1.135-.136a.246.246 0 0 1 .186-.048c.107.02.183.128.202.236.032.18-.04.396-.114.555a1.097 1.097 0 0 1-.31.415c-.06.044-.114.088-.178.116-.028.008-.063.028-.115.028h-.016c-.055 0-.114-.028-.126-.088a.827.827 0 0 1 .015-.367m4.308-.184c-.004.072-.024.148-.028.223a4.91 4.91 0 0 0 0 .779c.012.152.047.3-.016.444a1.069 1.069 0 0 1-.567.643.555.555 0 0 1-.245.056c-.02 0-.04-.004-.06-.004-.12 0-.214-.092-.265-.18a.871.87 0 0 1-.1-.272 2.129 2.129 0 0 1 .072-1.122c.08-.22.202-.435.38-.594a.874.874 0 0 1 .563-.24.31.31 0 0 1 .206.064c.04.044.06.104.056.164a.05.05 0 0 1 .004.04m6.43 4.653c-.015.044-.06.104-.08.14-.042.08-.102.163-.161.235a2.562 2.562 0 0 1-.317.304c-.238.18-.503.311-.777.387a2.025 2.025 0 0 1-.487.072h-.04a.795.795 0 0 1-.515-.18.433.433 0 0 1-.158-.25.537.537 0 0 1 .047-.305.776.776 0 0 1 .38-.383c.326-.16.682-.176 1.019-.16.139.004.265.012.4.02.107.004.218.012.325.024.056 0 .115.004.17.012.044.004.092-.004.135.008.06.004.068.036.06.076"
                  />
                </svg>
              </a>
              <!-- Credly end -->

              <!-- GitHub start -->
              <a
                href="https://github.com/musyaml"
                target="_blank"
                class="mr-3 flex h-9 w-9 items-center justify-center rounded-full border border-slate-300 text-slate-300 hover:border-primary hover:bg-primary hover:text-white"
              >
                <svg
                  role="img"
                  width="20"
                  class="fill-current"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>GitHub</title>
                  <path
                    d="M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"
                  />
                </svg>
              </a>
              <!-- GitHub end -->

              <!-- Instagram start -->
              <a
                href="https://instagram.com/musyaml"
                target="_blank"
                class="mr-3 flex h-9 w-9 items-center justify-center rounded-full border border-slate-300 text-slate-300 hover:border-primary hover:bg-primary hover:text-white"
              >
                <svg
                  role="img"
                  width="20"
                  class="fill-current"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Instagram</title>
                  <path
                    d="M12 0C8.74 0 8.333.015 7.053.072 5.775.132 4.905.333 4.14.63c-.789.306-1.459.717-2.126 1.384S.935 3.35.63 4.14C.333 4.905.131 5.775.072 7.053.012 8.333 0 8.74 0 12s.015 3.667.072 4.947c.06 1.277.261 2.148.558 2.913.306.788.717 1.459 1.384 2.126.667.666 1.336 1.079 2.126 1.384.766.296 1.636.499 2.913.558C8.333 23.988 8.74 24 12 24s3.667-.015 4.947-.072c1.277-.06 2.148-.262 2.913-.558.788-.306 1.459-.718 2.126-1.384.666-.667 1.079-1.335 1.384-2.126.296-.765.499-1.636.558-2.913.06-1.28.072-1.687.072-4.947s-.015-3.667-.072-4.947c-.06-1.277-.262-2.149-.558-2.913-.306-.789-.718-1.459-1.384-2.126C21.319 1.347 20.651.935 19.86.63c-.765-.297-1.636-.499-2.913-.558C15.667.012 15.26 0 12 0zm0 2.16c3.203 0 3.585.016 4.85.071 1.17.055 1.805.249 2.227.415.562.217.96.477 1.382.896.419.42.679.819.896 1.381.164.422.36 1.057.413 2.227.057 1.266.07 1.646.07 4.85s-.015 3.585-.074 4.85c-.061 1.17-.256 1.805-.421 2.227-.224.562-.479.96-.899 1.382-.419.419-.824.679-1.38.896-.42.164-1.065.36-2.235.413-1.274.057-1.649.07-4.859.07-3.211 0-3.586-.015-4.859-.074-1.171-.061-1.816-.256-2.236-.421-.569-.224-.96-.479-1.379-.899-.421-.419-.69-.824-.9-1.38-.165-.42-.359-1.065-.42-2.235-.045-1.26-.061-1.649-.061-4.844 0-3.196.016-3.586.061-4.861.061-1.17.255-1.814.42-2.234.21-.57.479-.96.9-1.381.419-.419.81-.689 1.379-.898.42-.166 1.051-.361 2.221-.421 1.275-.045 1.65-.06 4.859-.06l.045.03zm0 3.678c-3.405 0-6.162 2.76-6.162 6.162 0 3.405 2.76 6.162 6.162 6.162 3.405 0 6.162-2.76 6.162-6.162 0-3.405-2.76-6.162-6.162-6.162zM12 16c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm7.846-10.405c0 .795-.646 1.44-1.44 1.44-.795 0-1.44-.646-1.44-1.44 0-.794.646-1.439 1.44-1.439.793-.001 1.44.645 1.44 1.439z"
                  />
                </svg>
              </a>
              <!-- Instagram end -->

              <!-- LinkedIn start -->
              <a
                href="https://linkedin.com/in/muhammadsyaamilmuzhaffar"
                target="_blank"
                class="mr-3 flex h-9 w-9 items-center justify-center rounded-full border border-slate-300 text-slate-300 hover:border-primary hover:bg-primary hover:text-white"
              >
                <svg
                  role="img"
                  width="20"
                  class="fill-current"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>LinkedIn</title>
                  <path
                    d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"
                  />
                </svg>
              </a>
              <!-- LinkedIn end -->

              <!-- Medium start -->
              <a
                href="https://medium.com/@musyaml"
                target="_blank"
                class="mr-3 flex h-9 w-9 items-center justify-center rounded-full border border-slate-300 text-slate-300 hover:border-primary hover:bg-primary hover:text-white"
              >
                <svg
                  role="img"
                  width="20"
                  class="fill-current"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Medium</title>
                  <path
                    d="M13.54 12a6.8 6.8 0 01-6.77 6.82A6.8 6.8 0 010 12a6.8 6.8 0 016.77-6.82A6.8 6.8 0 0113.54 12zM20.96 12c0 3.54-1.51 6.42-3.38 6.42-1.87 0-3.39-2.88-3.39-6.42s1.52-6.42 3.39-6.42 3.38 2.88 3.38 6.42M24 12c0 3.17-.53 5.75-1.19 5.75-.66 0-1.19-2.58-1.19-5.75s.53-5.75 1.19-5.75C23.47 6.25 24 8.83 24 12z"
                  />
                </svg>
              </a>
              <!-- Medium end -->

              <!-- Spotify start -->
              <a
                href="https://open.spotify.com/user/31tfnlfrewoxpjcbcirzizwboyeu?si=928057b7678c4320"
                target="_blank"
                class="mr-3 flex h-9 w-9 items-center justify-center rounded-full border border-slate-300 text-slate-300 hover:border-primary hover:bg-primary hover:text-white"
              >
                <svg
                  role="img"
                  width="20"
                  class="fill-current"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Spotify</title>
                  <path
                    d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.419 1.56-.299.421-1.02.599-1.559.3z"
                  />
                </svg>
              </a>
              <!-- Spotify end -->

              <!-- Twitter start -->
              <a
                href="https://twitter.com/musyaml"
                target="_blank"
                class="mr-3 flex h-9 w-9 items-center justify-center rounded-full border border-slate-300 text-slate-300 hover:border-primary hover:bg-primary hover:text-white"
              >
                <svg
                  role="img"
                  width="20"
                  class="fill-current"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Twitter</title>
                  <path
                    d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"
                  />
                </svg>
              </a>
              <!-- Twitter end -->
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- About Section End -->

    <!-- Skill Section Start -->
    <section id="skill" class="bg-slate-100 pt-36 pb-32 dark:bg-slate-800">
      <div class="container">
        <div class="w-full px-4">
          <div class="mx-auto mb-16 max-w-xl text-center">
            <h4 class="mb-2 text-lg font-semibold text-primary">Skill</h4>
            <h2
              class="mb-4 text-3xl font-bold text-dark dark:text-white sm:text-4xl lg:text-5xl"
            >
              Keahlian
            </h2>
            <p class="text-md font-medium text-secondary md:text-lg">
              Kemampuan yang Saya miliki
            </p>
          </div>
        </div>

        <div class="flex flex-wrap">
          <!-- Prototyping -->
          <div class="w-full px-4 lg:w-1/2 xl:w-1/3">
            <div
              class="mb-10 overflow-hidden rounded-xl bg-white shadow-lg dark:bg-dark"
            >
              <img src="" alt="" class="w-full" />
              <div class="py-8 px-6">
                <h3>
                  <a
                    href="#"
                    class="mb-3 block truncate text-xl font-semibold text-dark hover:text-primary dark:text-white"
                    >Prototyping</a
                  >
                </h3>
                <p class="mb-6 text-base font-medium text-secondary">
                  Membuat prototipe untuk menguji coba sebuah konsep yang sudah
                  dibuat untuk memastikan berjalan dengan semestinya.
                </p>
              </div>
            </div>
          </div>
          <!-- Prototyping end -->

          <!-- Sitemap -->
          <div class="w-full px-4 lg:w-1/2 xl:w-1/3">
            <div
              class="mb-10 overflow-hidden rounded-xl bg-white shadow-lg dark:bg-dark"
            >
              <img src="" alt="" class="w-full" />
              <div class="py-8 px-6">
                <h3>
                  <a
                    href="#"
                    class="mb-3 block truncate text-xl font-semibold text-dark hover:text-primary dark:text-white"
                    >Sitemap</a
                  >
                </h3>
                <p class="mb-6 text-base font-medium text-secondary">
                  Membuat sebuah peta yang menunjukkan struktur dan tata
                  penggunaan website atau aplikasi agar lebih mudah dipahami dan
                  dibaca.
                </p>
              </div>
            </div>
          </div>
          <!-- Sitemap end -->

          <!-- UI/UX Design start -->
          <div class="w-full px-4 lg:w-1/2 xl:w-1/3">
            <div
              class="mb-10 overflow-hidden rounded-xl bg-white shadow-lg dark:bg-dark"
            >
              <img src="" alt="" class="w-full" />
              <div class="py-8 px-6">
                <h3>
                  <a
                    href="#"
                    class="mb-3 block truncate text-xl font-semibold text-dark hover:text-primary dark:text-white"
                    >UI/UX Design</a
                  >
                </h3>
                <p class="mb-6 text-base font-medium text-secondary">
                  Merancang antarmuka pengguna dan pengalaman pengguna agar
                  indah dipandang dan nyaman ketika digunakan.
                </p>
              </div>
            </div>
          </div>
          <!-- UI/UX Design end -->

          <!-- Video Editor start -->
          <div class="w-full px-4 lg:w-1/2 xl:w-1/3">
            <div
              class="mb-10 overflow-hidden rounded-xl bg-white shadow-lg dark:bg-dark"
            >
              <img src="" alt="" class="w-full" />
              <div class="py-8 px-6">
                <h3>
                  <a
                    href="#"
                    class="mb-3 block truncate text-xl font-semibold text-dark hover:text-primary dark:text-white"
                    >Video Editor</a
                  >
                </h3>
                <p class="mb-6 text-base font-medium text-secondary">
                  Mengedit dan menyusun footage sebuah video agar lebih
                  terstruktur dan menarik ketika dipandang.
                </p>
              </div>
            </div>
          </div>
          <!-- Video Editor end -->

          <!-- Wireframing start -->
          <div class="w-full px-4 lg:w-1/2 xl:w-1/3">
            <div
              class="mb-10 overflow-hidden rounded-xl bg-white shadow-lg dark:bg-dark"
            >
              <img src="" alt="" class="w-full" />
              <div class="py-8 px-6">
                <h3>
                  <a
                    href="#"
                    class="mb-3 block truncate text-xl font-semibold text-dark hover:text-primary dark:text-white"
                    >Wireframing</a
                  >
                </h3>
                <p class="mb-6 text-base font-medium text-secondary">
                  Merancang kerangka atau sketsa konsep awal agar memudahkan
                  dalam menentukan alur dari sebuah desain.
                </p>
              </div>
            </div>
          </div>
          <!-- Wireframing end -->
        </div>
      </div>
    </section>
    <!-- Skill Section End -->

    <!-- Portfolio Section Start -->
    <section id="portfolio" class="bg-slate-800 pt-36 pb-16 dark:bg-slate-300">
      <div class="container">
        <!-- Proyek Terbaru start -->
        <div class="w-full px-4">
          <div class="mx-auto mb-16 max-w-xl text-center">
            <h4 class="mb-2 text-lg font-semibold text-primary">Portfolio</h4>
            <h2
              class="mb-4 text-3xl font-bold text-white dark:text-dark sm:text-4xl lg:text-5xl"
            >
              Proyek Terbaru
            </h2>
            <p class="text-md font-medium text-secondary md:text-lg">
              Proyek yang sudah Saya kerjakan
            </p>
          </div>
        </div>

        <div
          class="flex w-full flex-wrap justify-center px-4 xl:mx-auto xl:w-10/12"
        >
          <div class="mb-12 p-4 md:w-1/2">
            <div class="overflow-hidden rounded-md shadow-md">
              <img
                src="dist/img/portfolio/project/1.png"
                alt="Landing Page Muhammad Syaamil Muzhaffar"
                width="w-full"
              />
            </div>
            <h3
              class="mt-5 mb-3 text-xl font-semibold text-white dark:text-dark"
            >
              Personal Portfolio
            </h3>
            <p class="text-base font-medium text-secondary">
              Dalam pembuatannya saya memakai teknologi CSS, Git, Github Pages,
              HTML, JavaScript, Node.js, dan Tailwind CSS dengan pengerjaannya
              selama 4 minggu dan rutin saya perbarui. Personal portfolio ini
              saya buat karena saya belum mempunyainya.
            </p>
          </div>
          <div class="mb-12 p-4 md:w-1/2">
            <div class="overflow-hidden rounded-md shadow-md">
              <img
                src="dist/img/portfolio/project/2.png"
                alt="KSPM Universitas Mercu Buana"
                width="w-full"
              />
            </div>
            <h3
              class="mt-5 mb-3 text-xl font-semibold text-white dark:text-dark"
            >
              KSPM Universitas Mercu Buana
            </h3>
            <p class="text-base font-medium text-secondary">
              Saya dipercaya oleh KSPM Mercu Buana untuk merancang tampilan dari
              website mereka, karena mereka bilang KSPM Mercu Buana itu sendiri
              belum mempunyai website resminya. Dengan pengerjaannya saya
              memakan waktu selama 3 hari. Peran saya disini sebagai designer
              dengan menggunakan software Figma.
            </p>
          </div>
          <div class="mb-12 p-4 md:w-1/2">
            <div class="overflow-hidden rounded-md shadow-md">
              <img
                src="dist/img/portfolio/project/3.png"
                alt="Coming Soon.."
                width="w-full"
              />
            </div>
            <h3
              class="mt-5 mb-3 text-xl font-semibold text-white dark:text-dark"
            >
              Coming Soon..
            </h3>
            <p class="text-base font-medium text-secondary">
              Belum tersedia untuk saat ini.
            </p>
          </div>
          <div class="mb-12 p-4 md:w-1/2">
            <div class="overflow-hidden rounded-md shadow-md">
              <img
                src="dist/img/portfolio/project/4.png"
                alt="Coming Soon.."
                width="w-full"
              />
            </div>
            <h3
              class="mt-5 mb-3 text-xl font-semibold text-white dark:text-dark"
            >
              Coming Soon..
            </h3>
            <p class="text-base font-medium text-secondary">
              Belum tersedia untuk saat ini.
            </p>
          </div>
        </div>
        <!-- Proyek Terbaru end -->

        <!-- Prestasi start -->
        <div class="w-full px-4">
          <div class="mx-auto mb-16 max-w-xl text-center">
            <h2
              class="mb-4 text-3xl font-bold text-white dark:text-dark sm:text-4xl lg:text-5xl"
            >
              Prestasi
            </h2>
            <p class="text-md font-medium text-secondary md:text-lg">
              Prestasi yang sudah Saya raih
            </p>
          </div>
        </div>

        <div
          class="flex w-full flex-wrap justify-center px-4 xl:mx-auto xl:w-10/12"
        >
          <div class="mb-12 p-4 md:w-1/2">
            <div class="overflow-hidden rounded-md shadow-md">
              <img
                src="dist/img/portfolio/achievement/1.png"
                alt="Dies Natalis Sistem Informasi UI/UX Competition 2024"
                width="w-full"
              />
            </div>
            <h3
              class="mt-5 mb-3 text-xl font-semibold text-white dark:text-dark"
            >
              Dies Natalis Sistem Informasi UI/UX Competition 2024
            </h3>
            <p class="text-base font-medium text-secondary">
              Saya mengikuti kompetisi UI/UX di kampus Saya yang terdiri 1 tim 3
              anggota. Peran Saya dalam tim sebagai pengkoreksi desain dan
              menciptakan ide baru.
            </p>
          </div>
          <div class="mb-12 p-4 md:w-1/2">
            <div class="overflow-hidden rounded-md shadow-md">
              <img
                src="dist/img/portfolio/achievement/2.png"
                alt="Coming Soon.."
                width="w-full"
              />
            </div>
            <h3
              class="mt-5 mb-3 text-xl font-semibold text-white dark:text-dark"
            >
              Coming Soon..
            </h3>
            <p class="text-base font-medium text-secondary">
              Belum tersedia untuk saat ini.
            </p>
          </div>
          <div class="mb-12 p-4 md:w-1/2">
            <div class="overflow-hidden rounded-md shadow-md">
              <img
                src="dist/img/portfolio/achievement/3.png"
                alt="Coming Soon.."
                width="w-full"
              />
            </div>
            <h3
              class="mt-5 mb-3 text-xl font-semibold text-white dark:text-dark"
            >
              Coming Soon..
            </h3>
            <p class="text-base font-medium text-secondary">
              Belum tersedia untuk saat ini.
            </p>
          </div>
          <div class="mb-12 p-4 md:w-1/2">
            <div class="overflow-hidden rounded-md shadow-md">
              <img
                src="dist/img/portfolio/achievement/4.png"
                alt="Coming Soon.."
                width="w-full"
              />
            </div>
            <h3
              class="mt-5 mb-3 text-xl font-semibold text-white dark:text-dark"
            >
              Coming Soon..
            </h3>
            <p class="text-base font-medium text-secondary">
              Belum tersedia untuk saat ini.
            </p>
          </div>
        </div>
        <!-- Prestasi end -->
      </div>
    </section>
    <!-- Portfolio Section End -->

    <!-- Tech Stack Section Start -->
    <section id="techstack" class="bg-slate-100 pt-36 pb-32 dark:bg-dark">
      <div class="container">
        <div class="w-full px-4">
          <div class="mx-auto mb-16 text-center">
            <h4 class="mb-2 text-lg font-semibold text-primary">Tech Stack</h4>
            <h2
              class="mb-4 text-3xl font-bold text-dark dark:text-white sm:text-4xl lg:text-5xl"
            >
              Teknologi yang Digunakan
            </h2>
            <p class="text-md font-medium text-secondary md:text-lg">
              Teknologi yang Saya pakai dalam pembuatan personal portfolio
            </p>
          </div>
        </div>

        <div class="w-full px-4">
          <div class="flex flex-wrap items-center justify-center">
            <!-- CSS (Cascading Style Sheets) -->
            <a
              href="https://www.w3.org/Style/CSS"
              target="_blank"
              class="mx-4 max-w-[120px] py-4 opacity-60 grayscale transition duration-500 hover:opacity-100 hover:grayscale-0 lg:mx-6 xl:mx-8"
            >
              <img src="assets/icons/tech-stacks/css3.svg" alt="CSS" />
            </a>

            <!-- Git -->
            <a
              href="https://git-scm.com/"
              target="_blank"
              class="mx-4 max-w-[120px] py-4 opacity-60 grayscale transition duration-500 hover:opacity-100 hover:grayscale-0 lg:mx-6 xl:mx-8"
            >
              <img src="assets/icons/tech-stacks/git.svg" alt="Git" />
            </a>

            <!-- GitHub Pages -->
            <a
              href="https://pages.github.com"
              target="_blank"
              class="mx-4 max-w-[120px] py-4 opacity-60 grayscale transition duration-500 hover:opacity-100 hover:grayscale-0 lg:mx-6 xl:mx-8"
            >
              <img
                src="assets/icons/tech-stacks/githubpages.svg"
                alt="GitHub"
              />
            </a>

            <!-- HTML (HyperText Markup Language) -->
            <a
              href="https://dev.w3.org/html5/spec-LC"
              target="_blank"
              class="mx-4 max-w-[120px] py-4 opacity-60 grayscale transition duration-500 hover:opacity-100 hover:grayscale-0 lg:mx-6 xl:mx-8"
            >
              <img src="assets/icons/tech-stacks/html5.svg" alt="HTML" />
            </a>

            <!-- JavaScript -->
            <a
              href="https://www.javascripttutorial.net"
              target="_blank"
              class="mx-4 max-w-[120px] py-4 opacity-60 grayscale transition duration-500 hover:opacity-100 hover:grayscale-0 lg:mx-6 xl:mx-8"
            >
              <img
                src="assets/icons/tech-stacks/javascript.svg"
                alt="JavaScript"
              />
            </a>

            <!-- Node.js -->
            <a
              href="https://nodejs.org"
              target="_blank"
              class="mx-4 max-w-[120px] py-4 opacity-60 grayscale transition duration-500 hover:opacity-100 hover:grayscale-0 lg:mx-6 xl:mx-8"
            >
              <img src="assets/icons/tech-stacks/nodejs.svg" alt="Node.js" />
            </a>

            <!-- Tailwind CSS -->
            <a
              href="https://tailwindcss.com"
              target="_blank"
              class="mx-4 max-w-[220px] py-4 opacity-60 grayscale transition duration-500 hover:opacity-100 hover:grayscale-0 lg:mx-6 xl:mx-8"
            >
              <img
                src="assets/icons/tech-stacks/tailwindcss.svg"
                alt="Tailwind CSS"
              />
            </a>
          </div>
        </div>
      </div>
    </section>
    <!-- Tech Stack Section End -->

    <!-- Contact Section Start -->
    <section id="contact" class="pt-36 pb-32 dark:bg-slate-800">
      <div class="container">
        <div class="w-full px-4">
          <div class="mx-auto mb-16 max-w-xl text-center">
            <h4 class="mb-2 text-lg font-semibold text-primary">Contact</h4>
            <h2
              class="mb-4 text-3xl font-bold text-dark dark:text-white sm:text-4xl lg:text-5xl"
            >
              Hubungi Saya
            </h2>
            <p class="text-md font-medium text-secondary md:text-lg">
              Silakan isi form di bawah ini untuk menghubungi Saya
            </p>
          </div>
        </div>

        <form name="personal-portfolio-contact-form">
          <div class="w-full lg:mx-auto lg:w-2/3">
            <div class="mb-8 w-full px-4">
              <label for="name" class="text-base font-bold text-primary"
                >Nama</label
              >
              <input
                type="text"
                id="name"
                name="nama"
                class="w-full rounded-md bg-slate-200 p-3 text-dark focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
              />
            </div>
            <div class="mb-8 w-full px-4">
              <label for="email" class="text-base font-bold text-primary"
                >Email</label
              >
              <input
                type="email"
                id="email"
                name="email"
                class="w-full rounded-md bg-slate-200 p-3 text-dark focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
              />
            </div>
            <div class="mb-8 w-full px-4">
              <label for="message" class="text-base font-bold text-primary"
                >Pesan</label
              >
              <textarea
                type="email"
                id="email"
                name="pesan"
                class="h-32 w-full rounded-md bg-slate-200 p-3 text-dark focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
              ></textarea>
            </div>
            <div class="w-full px-4">
              <button
                type="submit"
                class="w-full rounded-full bg-primary py-3 px-8 text-base font-semibold text-white transition duration-500 hover:opacity-80 hover:shadow-lg"
              >
                Kirim
              </button>
            </div>
          </div>
        </form>
      </div>
    </section>
    <!-- Contact Section End -->

    <!-- Footer Start -->
    <footer class="bg-dark pt-24 pb-12">
      <div class="container">
        <div class="flex flex-wrap">
          <div class="mb-12 w-full px-4 font-medium text-slate-300 md:w-1/3">
            <h3 class="mb-5 text-xl font-semibold text-white">Hubungi Saya</h3>
            <p><EMAIL></p>
          </div>
          <div class="mb-12 w-full px-4 md:w-1/3">
            <h3 class="mb-5 text-xl font-semibold text-white">Skill</h3>
            <ul class="text-slate-300">
              <!-- Prototyping -->
              <li>
                <a
                  href="#skill"
                  class="mb-3 inline-block text-base hover:text-primary"
                  >Prototyping</a
                >
              </li>

              <!-- Sitemap -->
              <li>
                <a
                  href="#skill"
                  class="mb-3 inline-block text-base hover:text-primary"
                  >Sitemap</a
                >
              </li>

              <!-- UI/UX Design -->
              <li>
                <a
                  href="#skill"
                  class="mb-3 inline-block text-base hover:text-primary"
                  >UI/UX Design</a
                >
              </li>

              <!-- Video Editor -->
              <li>
                <a
                  href="#skill"
                  class="mb-3 inline-block text-base hover:text-primary"
                  >Video Editor</a
                >
              </li>

              <!-- Wireframing -->
              <li>
                <a
                  href="#skill"
                  class="mb-3 inline-block text-base hover:text-primary"
                  >Wireframing</a
                >
              </li>
            </ul>
          </div>
          <div class="mb-12 w-full px-4 md:w-1/3">
            <h3 class="mb-5 text-xl font-semibold text-white">Tautan</h3>
            <ul class="text-slate-300">
              <!-- Beranda -->
              <li>
                <a
                  href="#home"
                  class="mb-3 inline-block text-base hover:text-primary"
                  >Beranda</a
                >
              </li>

              <!-- Tentang Saya -->
              <li>
                <a
                  href="#about"
                  class="mb-3 inline-block text-base hover:text-primary"
                  >Tentang Saya</a
                >
              </li>

              <!-- Skill -->
              <li>
                <a
                  href="#skill"
                  class="mb-3 inline-block text-base hover:text-primary"
                  >Skill</a
                >
              </li>

              <!-- Portfolio -->
              <li>
                <a
                  href="#portfolio"
                  class="mb-3 inline-block text-base hover:text-primary"
                  >Portfolio</a
                >
              </li>

              <!-- Tech Stack -->
              <li>
                <a
                  href="#techstack"
                  class="mb-3 inline-block text-base hover:text-primary"
                  >Tech stack</a
                >
              </li>

              <!-- Contact -->
              <li>
                <a
                  href="#contact"
                  class="mb-3 inline-block text-base hover:text-primary"
                  >Contact</a
                >
              </li>
            </ul>
          </div>
        </div>

        <div class="w-full border-t border-slate-700 pt-10">
          <div class="mb-5 flex items-center justify-center">
            <!-- Credly start -->
            <a
              href="https://www.credly.com/users/musyaml"
              target="_blank"
              class="mr-3 flex h-9 w-9 items-center justify-center rounded-full border border-slate-300 text-slate-300 hover:border-primary hover:bg-primary hover:text-white"
            >
              <svg
                role="img"
                width="20"
                class="fill-current"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>Credly</title>
                <path
                  d="M23.8 13.154a.299.299 0 0 0-.101-.024.407.407 0 0 0-.202.048c-.06.028-.092.08-.127.136-.087.128-.15.268-.226.4-.107.187-.246.351-.38.515-.135.156-.286.291-.424.44-.028.027-.072.043-.107.027-.028-.016-.036-.056-.032-.088.04-.38.075-.763.123-1.138.02-.172.043-.336.063-.512.028-.247.056-.487.087-.735l.234-1.824c.02-.128.032-.372-.135-.52a.446.446 0 0 0-.233-.116.46.46 0 0 0-.254.06c-.226.16-.297.504-.365.76-.142.603-.178 1.241-.471 1.804a1.772 1.772 0 0 1-.202.316.668.668 0 0 1-.186.18.332.332 0 0 1-.246.051.365.365 0 0 1-.238-.207.871.87 0 0 1-.063-.324 4.499 4.499 0 0 1 .24-1.585c.045-.132.089-.252.104-.383.028-.156.028-.38-.114-.516-.131-.128-.337-.18-.504-.128-.194.056-.31.244-.372.392-.198.463-.25.95-.317 1.446-.044.327-.127.64-.293.926a2.717 2.717 0 0 1-.603.72c-.118.087-.222.123-.328.107a.376.376 0 0 1-.278-.208.875.875 0 0 1-.095-.315 3.361 3.36 0 0 1-.036-.616c.004-.223 0-.44.044-.658.075-.39.678-1.937.808-2.345.135-.407.262-.823.353-1.246.08-.38.123-.767.11-1.15-.007-.277-.07-.576-.288-.736a.611.61 0 0 0-.603-.048.968.968 0 0 0-.455.428 2.53 2.53 0 0 0-.226.59 12.01 12.01 0 0 0-.266 1.29c-.071.429-.138.848-.206 1.268-.06.355-.206 1.614-.261 1.88-.06.272-.175.54-.301.787-.131.268-.258.536-.408.791a.694.694 0 0 1-.175.224c-.08.06-.182.088-.27.048-.102-.048-.146-.176-.166-.292-.075-.435-.012-.875.072-1.302.083-.431.44-2.4.519-2.851.099-.532.24-1.05.285-1.59.028-.388.09-.88-.202-1.187-.115-.136-.31-.16-.44-.136-.174.036-.31.176-.388.296-.1.128-.186.28-.258.467-.115.284-.186.615-.261.91l-.032.129c-.083.383-.143.77-.186 1.162a16.95 16.948 0 0 0-.06.632c-.008.1-.016.203-.027.307 0 .08.007.168-.028.244a.304.304 0 0 1-.052.068c-.08.072-.202.06-.31.056-.557-.016-1.045.3-1.35.755-.18.252-.281.542-.39.834-.01.048-.034.1-.054.152-.051.143-.13.327-.222.511a3.037 3.037 0 0 1-.317.46 3.285 3.285 0 0 1-.384.41 1.123 1.123 0 0 1-.515.26c-.174.04-.384-.043-.543-.203a.916.916 0 0 1-.206-.54c-.004-.055-.004-.115.028-.163.05-.068.146-.072.23-.076a1.623 1.623 0 0 0 1.375-1.015c.138-.34.178-.698.122-1.046a1.193 1.193 0 0 0-.19-.48.9.9 0 0 0-.396-.323c-.293-.14-.658-.127-1.01.004-.575.232-.951.74-1.134 1.562l-.02.088c-.114.487-.23 1-.582 1.354-.127.12-.261.163-.368.143-.044-.004-.08-.04-.103-.075-.096-.16.003-.532.15-1a4.1 4.1 0 0 0 .1-.366.925.925 0 0 0-.108-.495.783.783 0 0 0-.372-.324c-.143-.064-.31-.06-.468-.06h-.047c-.044 0-.103 0-.151-.012a.215.215 0 0 1-.147-.127.485.485 0 0 1 .016-.232c.004-.02.012-.048.016-.072a.368.368 0 0 0-.162-.412.509.509 0 0 0-.468-.036.768.768 0 0 0-.364.348.769.769 0 0 0-.103.48c.04.13.07.32.043.475-.055.28-.222.51-.384.74-.04.05-.072.106-.107.16a4.96 4.96 0 0 1-.706.825c-.372.335-.804.575-1.232.67-.745.165-1.506-.06-1.91-.734-.222-.38-.32-.827-.348-1.266a5.425 5.425 0 0 1 .424-2.516c.328-.76.816-1.52 1.715-1.614.353-.04.753.083.912.4.115.23.075.506 0 .75-.072.244-.175.49-.18.75-.003.26.124.54.37.616.238.072.495-.08.634-.29.138-.21.186-.46.245-.704a6.282 6.281 0 0 1 .662-1.634c.139-.236.297-.488.254-.76a.543.543 0 0 0-.373-.415.543.543 0 0 0-.535.144c-.134.148-.206.371-.387.43-.17.06-.35-.055-.507-.134-.6-.32-1.336-.312-1.963-.048-.634.25-1.146.735-1.526 1.294C.462 8.53.098 9.508.022 10.48c-.027.34-.031.695 0 1.038.036.46.1.854.214 1.206.139.423.317.79.547 1.094.266.34.587.6.94.747.372.148.784.22 1.192.208a3.172 3.172 0 0 0 1.177-.283 4.29 4.29 0 0 0 1.026-.68c.309-.26.594-.559.84-.89.162-.224.309-.46.44-.708a4.83 4.83 0 0 0 .178-.383c.044-.104.087-.215.202-.26.056-.043.15-.02.202.013.064.04.115.075.135.135.048.116.02.232-.004.332v.012c-.028.1-.055.203-.091.303-.14.424-.238.811-.16 1.195.045.207.128.387.25.527a.84.84 0 0 0 .504.264c.246.04.51-.028.725-.132.143-.068.278-.156.397-.26.06-.06.122-.12.174-.184.044-.06.087-.147.178-.143a.15.15 0 0 1 .107.064c.028.031.04.071.06.115.23.52.776.84 1.335.84h.07c.27 0 .556-.093.79-.22.27-.14.48-.348.7-.552.02-.016.045-.04.073-.044.035-.008.07.012.099.044a.26.26 0 0 1 .047.1c.135.34.46.6.824.66a1.1 1.1 0 0 0 .99-.356c.056-.06.104-.128.167-.176.064-.044.15-.076.222-.044.107.04.135.164.182.268.107.235.357.371.615.375.289 0 .554-.148.764-.34.195-.183.353-.399.516-.61a.328.328 0 0 1 .106-.096c.04-.024.096-.028.13 0 .033.024.045.06.06.091.163.4.587.652 1.01.648.417-.004.809-.224 1.103-.516.095-.092.194-.2.32-.21.14-.017.207.114.254.22.072.142.115.238.25.338.158.116.36.152.547.1.17-.04.34-.156.47-.316.072-.088.112-.204.19-.284.092-.087.132.028.136.1.016.116.016.236.008.352-.016.236-.052.471-.08.703-.011.068-.02.136-.063.188-.06.068-.166.08-.253.064a2.898 2.898 0 0 0-.321-.028l-.14-.016c-.201-.012-.4-.036-.61-.044h-.185c-.404 0-.733.048-1.03.16-.48.187-.852.57-1.003 1.018a1.305 1.305 0 0 0-.052.64c.04.203.13.403.282.587.265.315.68.515 1.149.543.408.02.852-.064 1.292-.26.848-.367 1.482-1.094 1.696-1.95 0-.02.01-.039.023-.043.298-.104.57-.248.813-.428.245-.187.467-.399.65-.643.09-.12.174-.243.253-.37.07-.125.13-.257.202-.38a.906.906 0 0 0 .13-.316.411.411 0 0 0-.05-.328.257.257 0 0 0-.135-.124m-13.68-1.63c.017-.071.045-.14.06-.206a1.9 1.9 0 0 1 .262-.504c.04-.048.08-.1.135-.136a.246.246 0 0 1 .186-.048c.107.02.183.128.202.236.032.18-.04.396-.114.555a1.097 1.097 0 0 1-.31.415c-.06.044-.114.088-.178.116-.028.008-.063.028-.115.028h-.016c-.055 0-.114-.028-.126-.088a.827.827 0 0 1 .015-.367m4.308-.184c-.004.072-.024.148-.028.223a4.91 4.91 0 0 0 0 .779c.012.152.047.3-.016.444a1.069 1.069 0 0 1-.567.643.555.555 0 0 1-.245.056c-.02 0-.04-.004-.06-.004-.12 0-.214-.092-.265-.18a.871.87 0 0 1-.1-.272 2.129 2.129 0 0 1 .072-1.122c.08-.22.202-.435.38-.594a.874.874 0 0 1 .563-.24.31.31 0 0 1 .206.064c.04.044.06.104.056.164a.05.05 0 0 1 .004.04m6.43 4.653c-.015.044-.06.104-.08.14-.042.08-.102.163-.161.235a2.562 2.562 0 0 1-.317.304c-.238.18-.503.311-.777.387a2.025 2.025 0 0 1-.487.072h-.04a.795.795 0 0 1-.515-.18.433.433 0 0 1-.158-.25.537.537 0 0 1 .047-.305.776.776 0 0 1 .38-.383c.326-.16.682-.176 1.019-.16.139.004.265.012.4.02.107.004.218.012.325.024.056 0 .115.004.17.012.044.004.092-.004.135.008.06.004.068.036.06.076"
                />
              </svg>
            </a>
            <!-- Credly end -->

            <!-- GitHub start -->
            <a
              href="https://github.com/musyaml"
              target="_blank"
              class="mr-3 flex h-9 w-9 items-center justify-center rounded-full border border-slate-300 text-slate-300 hover:border-primary hover:bg-primary hover:text-white"
            >
              <svg
                role="img"
                width="20"
                class="fill-current"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>GitHub</title>
                <path
                  d="M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"
                />
              </svg>
            </a>
            <!-- GitHub end -->

            <!-- Instagram start -->
            <a
              href="https://instagram.com/musyaml"
              target="_blank"
              class="mr-3 flex h-9 w-9 items-center justify-center rounded-full border border-slate-300 text-slate-300 hover:border-primary hover:bg-primary hover:text-white"
            >
              <svg
                role="img"
                width="20"
                class="fill-current"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>Instagram</title>
                <path
                  d="M12 0C8.74 0 8.333.015 7.053.072 5.775.132 4.905.333 4.14.63c-.789.306-1.459.717-2.126 1.384S.935 3.35.63 4.14C.333 4.905.131 5.775.072 7.053.012 8.333 0 8.74 0 12s.015 3.667.072 4.947c.06 1.277.261 2.148.558 2.913.306.788.717 1.459 1.384 2.126.667.666 1.336 1.079 2.126 1.384.766.296 1.636.499 2.913.558C8.333 23.988 8.74 24 12 24s3.667-.015 4.947-.072c1.277-.06 2.148-.262 2.913-.558.788-.306 1.459-.718 2.126-1.384.666-.667 1.079-1.335 1.384-2.126.296-.765.499-1.636.558-2.913.06-1.28.072-1.687.072-4.947s-.015-3.667-.072-4.947c-.06-1.277-.262-2.149-.558-2.913-.306-.789-.718-1.459-1.384-2.126C21.319 1.347 20.651.935 19.86.63c-.765-.297-1.636-.499-2.913-.558C15.667.012 15.26 0 12 0zm0 2.16c3.203 0 3.585.016 4.85.071 1.17.055 1.805.249 2.227.415.562.217.96.477 1.382.896.419.42.679.819.896 1.381.164.422.36 1.057.413 2.227.057 1.266.07 1.646.07 4.85s-.015 3.585-.074 4.85c-.061 1.17-.256 1.805-.421 2.227-.224.562-.479.96-.899 1.382-.419.419-.824.679-1.38.896-.42.164-1.065.36-2.235.413-1.274.057-1.649.07-4.859.07-3.211 0-3.586-.015-4.859-.074-1.171-.061-1.816-.256-2.236-.421-.569-.224-.96-.479-1.379-.899-.421-.419-.69-.824-.9-1.38-.165-.42-.359-1.065-.42-2.235-.045-1.26-.061-1.649-.061-4.844 0-3.196.016-3.586.061-4.861.061-1.17.255-1.814.42-2.234.21-.57.479-.96.9-1.381.419-.419.81-.689 1.379-.898.42-.166 1.051-.361 2.221-.421 1.275-.045 1.65-.06 4.859-.06l.045.03zm0 3.678c-3.405 0-6.162 2.76-6.162 6.162 0 3.405 2.76 6.162 6.162 6.162 3.405 0 6.162-2.76 6.162-6.162 0-3.405-2.76-6.162-6.162-6.162zM12 16c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm7.846-10.405c0 .795-.646 1.44-1.44 1.44-.795 0-1.44-.646-1.44-1.44 0-.794.646-1.439 1.44-1.439.793-.001 1.44.645 1.44 1.439z"
                />
              </svg>
            </a>
            <!-- Instagram end -->

            <!-- LinkedIn start -->
            <a
              href="https://linkedin.com/in/muhammadsyaamilmuzhaffar"
              target="_blank"
              class="mr-3 flex h-9 w-9 items-center justify-center rounded-full border border-slate-300 text-slate-300 hover:border-primary hover:bg-primary hover:text-white"
            >
              <svg
                role="img"
                width="20"
                class="fill-current"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>LinkedIn</title>
                <path
                  d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"
                />
              </svg>
            </a>
            <!-- LinkedIn end -->

            <!-- Medium start -->
            <a
              href="https://medium.com/@musyaml"
              target="_blank"
              class="mr-3 flex h-9 w-9 items-center justify-center rounded-full border border-slate-300 text-slate-300 hover:border-primary hover:bg-primary hover:text-white"
            >
              <svg
                role="img"
                width="20"
                class="fill-current"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>Medium</title>
                <path
                  d="M13.54 12a6.8 6.8 0 01-6.77 6.82A6.8 6.8 0 010 12a6.8 6.8 0 016.77-6.82A6.8 6.8 0 0113.54 12zM20.96 12c0 3.54-1.51 6.42-3.38 6.42-1.87 0-3.39-2.88-3.39-6.42s1.52-6.42 3.39-6.42 3.38 2.88 3.38 6.42M24 12c0 3.17-.53 5.75-1.19 5.75-.66 0-1.19-2.58-1.19-5.75s.53-5.75 1.19-5.75C23.47 6.25 24 8.83 24 12z"
                />
              </svg>
            </a>
            <!-- Medium end -->

            <!-- Spotify start -->
            <a
              href="https://open.spotify.com/user/31tfnlfrewoxpjcbcirzizwboyeu?si=928057b7678c4320"
              target="_blank"
              class="mr-3 flex h-9 w-9 items-center justify-center rounded-full border border-slate-300 text-slate-300 hover:border-primary hover:bg-primary hover:text-white"
            >
              <svg
                role="img"
                width="20"
                class="fill-current"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>Spotify</title>
                <path
                  d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.419 1.56-.299.421-1.02.599-1.559.3z"
                />
              </svg>
            </a>
            <!-- Spotify end -->

            <!-- Twitter start -->
            <a
              href="https://twitter.com/musyaml"
              target="_blank"
              class="mr-3 flex h-9 w-9 items-center justify-center rounded-full border border-slate-300 text-slate-300 hover:border-primary hover:bg-primary hover:text-white"
            >
              <svg
                role="img"
                width="20"
                class="fill-current"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>Twitter</title>
                <path
                  d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"
                />
              </svg>
            </a>
            <!-- Twitter end -->
          </div>
          <p class="text-center text-xs font-medium text-slate-500">
            Copyright © 2024 Muhammad Syaamil Muzhaffar
          </p>
        </div>
      </div>
    </footer>
    <!-- Footer End -->

    <!-- Back to top Start -->
    <a
      href="#home"
      class="fixed bottom-4 right-4 z-[9999] hidden h-14 w-14 items-center justify-center rounded-full bg-primary p-4 hover:animate-pulse"
      id="to-top"
    >
      <span class="mt-2 block h-5 w-5 rotate-45 border-t-2 border-l-2"></span>
    </a>
    <!-- Back to top End -->

    <script src="dist/js/script.js"></script>
  </body>
</html>
